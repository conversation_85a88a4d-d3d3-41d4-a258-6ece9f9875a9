cad = window.cad || {},
cad.service = "/draw",
cad.timeout = 6e4,
cad.math = {
    calcDist: function(t, i, s, e) {
        var n = function(t, i, s, e) {
            return Math.sqrt(Math.pow(s - t, 2) + Math.pow(e - i, 2))
        };
        return arguments.length < 4 ? n(t.pageX, t.pageY, i.pageX, i.pageY) : n(t, i, s, e)
    },
    subPoint: function(t, i) {
        return {
            x: t.x - i.x,
            y: t.y - i.y,
            z: t.z - i.z
        }
    },
    addPoint: function(t, i) {
        return {
            x: t.x + i.x,
            y: t.y + i.y,
            z: t.z + i.z
        }
    },
    centerPoint: function(t, i) {
        return {
            x: (t.x + i.x) / 2,
            y: (t.y + i.y) / 2,
            z: 0
        }
    },
    buildRotMatrix: function(t, i, s) {
        var e = function(e, n, a) {
            var h = 0
              , r = 0
              , o = i * (Math.PI / 180)
              , c = o / (.5 * Math.PI)
              , l = 0 | c
              , d = c % 1
              , u = !1;
            if (Math.abs(1 - Math.abs(d)) < s ? (l += d < 0 ? -1 : 1,
            u = !0) : Math.abs(d) < s && (u = !0),
            u)
                switch (l % 4) {
                case 0:
                case 4:
                    h = 0,
                    r = 1;
                    break;
                case -3:
                case 1:
                    h = 1,
                    r = 0;
                    break;
                case -2:
                case 2:
                    h = 0,
                    r = -1;
                    break;
                case -1:
                case 3:
                    h = -1,
                    r = 0
                }
            else
                h = Math.sin(o),
                r = Math.cos(o);
            2 === t && (h = -h),
            a[e][e] = r,
            a[n][n] = r,
            a[e][n] = h,
            a[n][e] = -h
        }
          , n = cad.math.initMatrix();
        switch (t) {
        case 1:
            e(1, 2, n);
            break;
        case 2:
            e(0, 2, n);
            break;
        case 3:
            e(0, 1, n)
        }
        return n
    },
    multiplyMatrix: function(t, i) {
        for (var s = [], e = 0; e < i.length; e++) {
            s[e] = [];
            for (var n = 0; n < t[0].length; n++) {
                for (var a = 0, h = 0; h < t.length; h++)
                    a += t[h][n] * i[e][h];
                s[e].push(a)
            }
        }
        return s
    },
    affineTransformPoint: function(t, i) {
        var s = {};
        return s.x = t.x * i[0][0] + t.y * i[1][0] + t.z * i[2][0],
        s.y = t.x * i[0][1] + t.y * i[1][1] + t.z * i[2][1],
        s.z = t.x * i[0][2] + t.y * i[1][2] + t.z * i[2][2],
        s
    },
    transformPoint: function(t, i) {
        var s = {};
        return s.x = t.x * i[0][0] + t.y * i[1][0] + t.z * i[2][0] + i[3][0],
        s.y = t.x * i[0][1] + t.y * i[1][1] + t.z * i[2][1] + i[3][1],
        s.z = t.x * i[0][2] + t.y * i[1][2] + t.z * i[2][2] + i[3][2],
        s
    },
    normalizeMatrix: function(t) {
        var i = function(t, i, s) {
            var e = t * t + i * i + s * s;
            return Math.sqrt(e)
        }
          , s = function(t) {
            return {
                x: i(t[0][0], t[0][1], t[0][2]),
                y: i(t[1][0], t[1][1], t[1][2]),
                z: i(t[2][0], t[2][1], t[2][2])
            }
        }(t)
          , e = {
            x: 1 / s.x,
            y: -1 / s.y,
            z: 1 / s.z
        };
        return this.matScale(t, e),
        t[3] = [0, 0, 0, 1],
        s
    },
    matScale: function(t, i) {
        var s = t;
        return s[0][0] *= i.x,
        s[1][0] *= i.x,
        s[2][0] *= i.x,
        s[3][0] *= i.x,
        s[0][1] *= i.y,
        s[1][1] *= i.y,
        s[2][1] *= i.y,
        s[3][1] *= i.y,
        s[0][2] *= i.z,
        s[1][2] *= i.z,
        s[2][2] *= i.z,
        s[3][2] *= i.z,
        s
    },
    initMatrix: function(t) {
        return t || (t = []),
        t[0] = [1, 0, 0, 0],
        t[1] = [0, 1, 0, 0],
        t[2] = [0, 0, 1, 0],
        t[3] = [0, 0, 0, 1],
        t
    },
    matByScale: function(t) {
        var i = cad.math.initMatrix();
        return i[0][0] *= t,
        i[1][1] *= t,
        i[2][2] *= t,
        i
    },
    matByTranslate: function(t, i, s) {
        var e = cad.math.initMatrix();
        return e[3] = [t, i, s, 1],
        e
    },
    matXMat: function(t, i) {
        for (var s = [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], e = 0; e < 4; e++)
            s[0][e] = t[0][0] * i[0][e] + t[0][1] * i[1][e] + t[0][2] * i[2][e],
            s[1][e] = t[1][0] * i[0][e] + t[1][1] * i[1][e] + t[1][2] * i[2][e],
            s[2][e] = t[2][0] * i[0][e] + t[2][1] * i[1][e] + t[2][2] * i[2][e],
            s[3][e] = t[3][0] * i[0][e] + t[3][1] * i[1][e] + t[3][2] * i[2][e] + i[3][e];
        return s
    },
    matTranslate: function(t, i, s, e) {
        return cad.math.matXMat(t, cad.math.matByTranslate(i, s, e))
    },
    distanceFVector: function(t) {
        var i = Math.pow(t.x, 2) + Math.pow(t.y, 2) + Math.pow(t.z, 2);
        return Math.sqrt(i)
    },
    distanceFPoint: function(t, i) {
        return cad.math.distanceFVector(cad.math.subPoint(t, i))
    },
    ort: function(t) {
        var i = Math.sqrt(Math.pow(t.x, 2) + Math.pow(t.y, 2) + Math.pow(t.z, 2));
        return 0 == i && (i = 1),
        {
            x: t.x / i,
            y: t.y / i,
            z: t.z / i
        }
    },
    pointXScalar: function(t, i) {
        return {
            x: t.x * i,
            y: t.y * i,
            z: t.z * i
        }
    },
    normalizeFVector: function(t) {
        var i = cad.math.distanceFVector(t);
        return cad.math.pointXScalar(t, 1 / i)
    },
    dirFVector: function(t, i) {
        var s = cad.math.subPoint(i, t);
        return cad.math.normalizeFVector(s)
    },
    pointOnLine: function(t, i, s) {
        var e = cad.math.pointXScalar(i, s);
        return cad.math.addPoint(t, e)
    },
    isEqualPoints: function(t, i) {
        return Math.abs(t.x - i.x) <= 1e-5 && Math.abs(t.y - i.y) <= 1e-5
    },
    isPointInCircle: function(t, i, s, e) {
        return Math.sqrt(Math.pow(t.x - s, 2) + Math.pow(t.y - e, 2)) <= i + 5
    },
    matInverse: function(t) {
        var i, s = Math.pow(t[0][0], 2) + Math.pow(t[0][1], 2) + Math.pow(t[0][2], 2), e = Math.pow(t[1][0], 2) + Math.pow(t[1][1], 2) + Math.pow(t[1][2], 2), n = Math.pow(t[2][0], 2) + Math.pow(t[2][1], 2) + Math.pow(t[2][2], 2);
        i = t[1][0],
        t[1][0] = t[0][1],
        t[0][1] = i,
        i = t[2][0],
        t[2][0] = t[0][2],
        t[0][2] = i,
        i = t[1][2],
        t[1][2] = t[2][1],
        t[2][1] = i,
        0 != s && (t[0][0] = t[0][0] / s,
        t[0][1] = t[0][1] / s,
        t[0][2] = t[0][2] / s),
        0 != e && (t[1][0] = t[1][0] / e,
        t[1][1] = t[1][1] / e,
        t[1][2] = t[1][2] / e),
        0 != n && (t[2][0] = t[2][0] / n,
        t[2][1] = t[2][1] / n,
        t[2][2] = t[2][2] / n);
        var a = cad.math.pointXScalar(cad.math.affineTransformPoint({
            x: t[3][0],
            y: t[3][1],
            z: t[3][2]
        }, t), -1);
        return t[3][0] = a.x,
        t[3][1] = a.y,
        t[3][2] = a.z,
        t
    },
    screenToDC: function(t, i) {
        var s = cad.math.matInverse(cad.utils.copyArray(i))
          , e = 0;
        return 0 != i[2][2] && (e = -(i[3][2] + t.x * i[0][2] + t.y * i[1][2]) / i[2][2]),
        cad.math.transformPoint({
            x: t.x,
            y: t.y,
            z: e
        }, s)
    },
    screenToCAD2: function(t, i, s, e) {
        var n, a, h = t.x / s.width, r = 1 - t.y / s.height, o = cad.utils.copyArray(i), c = function(t, i) {
            return t.x * i.x + t.y * i.y + t.z * i.z
        }, l = {};
        if (function(t) {
            var i = 0
              , s = 0
              , n = cad.math.initMatrix()
              , a = function(a) {
                switch (t.NullCoord = a,
                a) {
                case 1:
                    n[0][0] = o[2][0],
                    n[1][1] = o[1][1],
                    n[2][2] = 0,
                    n[0][1] = o[2][1],
                    n[0][2] = 0,
                    n[1][0] = o[1][0],
                    n[1][2] = 0,
                    n[2][0] = 0,
                    n[2][1] = 0;
                    break;
                case 2:
                    n[0][0] = o[0][0],
                    n[1][1] = o[2][1],
                    n[2][2] = 0,
                    n[0][1] = o[0][1],
                    n[0][2] = 0,
                    n[1][0] = o[2][0],
                    n[1][2] = 0,
                    n[2][0] = 0,
                    n[2][1] = 0;
                    break;
                default:
                    n = cad.utils.copyArray(o)
                }
                i = Math.abs((e.right - e.left) * n[0][0]) + Math.abs((e.top - e.bottom) * n[1][0]),
                s = Math.abs((e.top - e.bottom) * n[1][1]) + Math.abs((e.right - e.left) * n[0][1])
            };
            if (a(0),
            Math.abs(Math.abs(n[1][2]) - 1) < .001 ? a(2) : Math.abs(Math.abs(n[0][2]) - 1) < .001 && a(1),
            0 == i || 0 == s)
                return !1;
            var c = Math.abs((e.z2 - e.z1) * n[2][0])
              , l = Math.abs((e.z2 - e.z1) * n[2][1])
              , d = e.z1 * n[2][0] / (c + i)
              , u = e.z1 * n[2][1] / (l + s);
            return t.RealLayoutCenter = {},
            t.RealLayoutCenter.x = .5 * (e.right + e.left),
            t.RealLayoutCenter.y = .5 * (e.top + e.bottom),
            t.RealLayoutCenter.z = 0,
            n[2][0] > 0 ? t.X = i * ((h + d) * (c + i) / i - .5) : t.X = i * ((h - c / (c + i) + d) * (c + i) / i - .5),
            n[2][1] > 0 ? t.Y = s * ((r + u) * (l + s) / s - .5) : t.Y = s * ((r - l / (l + s) + u) * (l + s) / s - .5),
            t.Rot = n,
            !0
        }(l)) {
            var d = {
                x: 0,
                y: 0,
                z: -1
            }
              , u = cad.math.transformPoint({
                x: 0,
                y: 0,
                z: 1
            }, l.Rot)
              , x = {
                x: l.X,
                y: l.Y,
                z: 0
            }
              , y = {
                x: 0,
                y: 0,
                z: 0
            }
              , f = {
                x: y.x - x.x,
                y: y.y - x.y,
                z: y.z - x.z
            }
              , p = c(u, d)
              , m = 0;
            0 != p && (m = c(u, f) / p);
            var g = {
                x: x.x + d.x * m,
                y: x.y + d.y * m,
                z: x.z + d.z * m
            };
            a = (n = l.Rot)[1][0],
            n[1][0] = n[0][1],
            n[0][1] = a,
            a = n[2][0],
            n[2][0] = n[0][2],
            n[0][2] = a,
            a = n[1][2],
            n[1][2] = n[2][1],
            n[2][1] = a;
            var v = cad.math.transformPoint(g, l.Rot)
              , w = {};
            switch (l.NullCoord) {
            case 1:
                w = {
                    x: 0,
                    y: v.y + l.RealLayoutCenter.y,
                    z: v.x + l.RealLayoutCenter.x
                };
                break;
            case 2:
                w = {
                    x: v.x + l.RealLayoutCenter.x,
                    y: 0,
                    z: v.y + l.RealLayoutCenter.y
                };
                break;
            default:
                w = {
                    x: v.x + l.RealLayoutCenter.x,
                    y: v.y + l.RealLayoutCenter.y,
                    z: 0
                }
            }
            return w
        }
        return {
            x: 0,
            y: 0,
            z: 0
        }
    }
},
cad.draw = {
    getCADFont: function(t, i) {
        return t.is_cad ? t : (cad_font = {
            is_cad: !0,
            height: i.pix_h * t.height,
            style: t.style
        },
        cad_font)
    },
    getFullFontName: function(t, i) {
        return arguments.length > 1 ? i.toString() + "px " + t.styleFont : t.style && t.height ? t.height.toString() + "px " + t.style.styleFont : t
    },
    drawPolyline: function(t, i, s) {
        if (!(t.length < 2)) {
            var e = i(t[0]);
            s.moveTo(e.x, e.y);
            for (var n = 1; n < t.length; n++)
                e = i(t[n]),
                s.lineTo(e.x, e.y)
        }
    },
    setTextParams: function(t, i, s, e, n, a, h) {
        var r = a(t)
          , o = e * (Math.PI / 180);
        if (h.textAlign = ["left", "center", "right", "left", , "center", ""][i],
        h.textBaseline = ["top", "", "middle", "top", "top", "bottom"][s],
        n.is_cad) {
            var c = cad.math.subPoint(a({
                x: Math.cos(o) * n.height,
                y: Math.sin(o) * n.height,
                z: 0
            }), a({
                x: 0,
                y: 0,
                z: 0
            }))
              , l = Math.round(cad.math.distanceFVector(c));
            h.font = cad.draw.getFullFontName(n.style, l),
            h.fontHeight = l
        } else
            h.font = cad.draw.getFullFontName(n),
            h.fontHeight = null;
        h.translate(r.x, r.y),
        h.rotate(-o)
    },
    getTextRect: function(t, i) {
        var s = i.measureText(t).width
          , e = 0
          , n = s / 2
          , a = (e = i.fontHeight ? i.fontHeight : parseInt(i.font)) / 2
          , h = null
          , r = null;
        return "left" == i.textAlign ? h = 0 : "right" == i.textAlign ? h = -s : "center" == i.textAlign && (h = -n),
        "top" == i.textBaseline ? r = 0 : "bottom" == i.textBaseline ? r = -e : "middle" == i.textBaseline && (r = -a),
        {
            x: Math.round(h),
            y: Math.round(r),
            width: Math.round(s),
            height: Math.round(e)
        }
    }
},
cad.geom = {
    calcAngle: function(t, i) {
        var s = i.x - t.x
          , e = i.y - t.y
          , n = s < 0
          , a = e < 0
          , h = s
          , r = e;
        s = Math.abs(s),
        e = Math.abs(e);
        var o = 0;
        return s > 1e-4 ? (o = 180 * Math.atan(e / s) / Math.PI,
        a && !n ? (o = 360 - o) >= 360 && (o = 0) : n && !a ? o = 180 - o : a && n && (o = 180 + o)) : o = a ? 270 : 90,
        {
            angle: o,
            deltaX: h,
            deltaY: r
        }
    },
    calcArea: function(t) {
        var i = t.length - 1
          , s = []
          , e = 0
          , n = 0
          , a = 0;
        if (i > 2) {
            s[n] = t[i];
            for (var h = 0; h <= i; h++)
                s[n = 1 ^ (a = n)] = t[h],
                e += (s[n].x + s[a].x) * (s[n].y - s[a].y);
            e = .5 * Math.abs(e)
        }
        return e
    },
    calcDist: function(t) {
        for (var i = 0, s = 1; s < t.length; s++)
            i += cad.math.calcDist(t[s - 1].x, t[s - 1].y, t[s].x, t[s].y);
        return i
    },
    createArrow: function(t, i, s, e) {
        if (t.length < 2)
            return [];
        var n = null
          , a = null
          , h = null
          , r = null;
        if (e) {
            if (n = t[0],
            a = t[1],
            cad.math.isEqualPoints(n, a))
                for (var o = 2; o < t.length && (a = t[o],
                cad.math.isEqualPoints(n, a)); o++)
                    ;
            r = [{
                x: 0,
                y: -s,
                z: 0
            }, {
                x: 0,
                y: 0,
                z: 0
            }, {
                x: i,
                y: s,
                z: 0
            }, {
                x: i,
                y: -s,
                z: 0
            }, {
                x: 0,
                y: 0,
                z: 0
            }, {
                x: 0,
                y: s,
                z: 0
            }],
            h = n
        } else {
            if (n = t[t.length - 2],
            a = t[t.length - 1],
            cad.math.isEqualPoints(n, a))
                for (o = t.length - 3; o >= 0 && (n = t[o],
                cad.math.isEqualPoints(n, a)); o--)
                    ;
            r = [{
                x: 0,
                y: -s,
                z: 0
            }, {
                x: 0,
                y: 0,
                z: 0
            }, {
                x: -i,
                y: s,
                z: 0
            }, {
                x: -i,
                y: -s,
                z: 0
            }, {
                x: 0,
                y: 0,
                z: 0
            }, {
                x: 0,
                y: s,
                z: 0
            }],
            h = a
        }
        var c = []
          , l = cad.geom.calcAngle(n, a)
          , d = cad.math.matTranslate(cad.math.buildRotMatrix(3, l.angle), h.x, h.y, h.z);
        return r.forEach(function(t) {
            c.push(cad.math.transformPoint(t, d))
        }),
        c
    },
    getCenterOfBox: function(t) {
        return t ? {
            x: (t.left + t.right) / 2,
            y: (t.top + t.bottom) / 2,
            z: 0
        } : null
    },
    getCenterOfPolyline: function(t) {
        var i = 0
          , s = 0
          , e = null
          , n = null
          , a = 0;
        t.length && (a = t.length);
        for (var h = a - 1; h > 0; h--)
            e = t[h - 1],
            n = t[h],
            i += cad.math.calcDist(e.x, e.y, n.x, n.y);
        var r = i / 2;
        i = 0;
        var o = null;
        for (h = a - 1; h > 0; h--)
            if (e = t[h - 1],
            n = t[h],
            (i += s = cad.math.calcDist(e.x, e.y, n.x, n.y)) > r) {
                var c = cad.math.subPoint(n, e)
                  , l = (i - r) / s;
                o = {
                    point1: e,
                    point2: n,
                    center: {
                        x: e.x + l * c.x,
                        y: e.y + l * c.y,
                        z: 0
                    }
                };
                break
            }
        return o
    },
    getOrtPoint: function(t, i, s) {
        var e = null
          , n = cad.geom.calcAngle(t, i);
        if (s = n.angle >= 90 || n.angle <= 270 ? s : -s,
        n.angle > 180) {
            var a = cad.math.matTranslate(cad.math.buildRotMatrix(3, n.angle - 360), i.x, i.y, i.z);
            e = cad.math.transformPoint({
                x: 0,
                y: s,
                z: 0
            }, a)
        } else {
            a = cad.math.matTranslate(cad.math.buildRotMatrix(3, n.angle), i.x, i.y, i.z);
            e = cad.math.transformPoint({
                x: 0,
                y: s,
                z: 0
            }, a)
        }
        return e
    },
    getPolylineBox: function(t, i) {
        if (t.length) {
            for (var s = t[0], e = {
                left: s.x,
                top: s.y,
                right: s.x,
                bottom: s.y
            }, n = t.length - 1; n > 0; n--)
                (s = t[n]).x < e.left && (e.left = s.x),
                s.x > e.right && (e.right = s.x),
                i ? (s.y > e.bottom && (e.bottom = s.y),
                s.y < e.top && (e.top = s.y)) : (s.y < e.bottom && (e.bottom = s.y),
                s.y > e.top && (e.top = s.y));
            return e
        }
        return null
    },
    getSizeOfBox2D: function(t) {
        return {
            x: Math.abs(t.right - t.left),
            y: Math(t.top - t.bottom),
            z: 0
        }
    }
},
cad.safeJsonParse = function(t) {
    try {
        return JSON.parse(t)
    } catch (i) {
        return t
    }
}
,
cad.setupAjax = function(t, i) {
    t.cross_domain && (i.crossDomain = !0,
    i.headers = {
        "Access-Control-Allow-Origin": t.origin,
        "Access-Control-Allow-Methods": "GET, PUT, POST, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Access-Control-Allow-Origin, Access-Control-Allow-Methods, Access-Control-Max-Age, Access-Control-Allow-Headers, Access-Control-Allow-Credentials",
        "Access-Control-Allow-Credentials": !0
    }),
    i.converters = {
        "text json": function(t) {
            return cad.safeJsonParse(t)
        }
    }
}
,
cad.get = function(t, i, s, e, n, a) {
    cad.ajax("GET", t, i, s, e, n, a)
}
,
cad.post = function(t, i, s, e, n, a) {
    cad.ajax("POST", t, i, s, e, n, a)
}
,
cad.ajax = function(t, i, s, e, n, a, h) {
    e || (e = "{}");
    var r = function(t) {
        console.log(t);
        try {
            var i = JSON.parse(t.responseText);
            i && "object" == typeof i ? console.log(i) : console.log(t)
        } catch (i) {
            console.log(t)
        }
    }
      , o = function(t, i, s) {
        s && console.log(s),
        i && console.log(i),
        t && r(t)
    };
    a || (a = o),
    $.ajaxSettings && ($.ajaxSettings.error || $.ajaxSetup({
        error: o
    }));
    try {
        var c = s;
        "object" == typeof s && (c = s[0] + "?" + cad.makeParams(s[1], i));
        var l = {
            type: t,
            url: i.service + "/" + c,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            data: JSON.stringify(e),
            async: h,
            timeout: cad.timeout,
            global: !0
        };
        cad.setupAjax(i, l),
        $.ajax(l).done(n).fail(a)
    } catch (t) {
        throw r(t),
        t
    }
}
,
cad.makeParams = function(t, i) {
    var s = [];
    for (var e in t)
        s.push(encodeURIComponent(e) + "=" + encodeURIComponent(t[e]));
    return i && i.cross_domain && s.push("is_cross=true"),
    s.push("rnd=" + Math.random()),
    s.join("&")
}
,
cad.extend = function(t, i) {
    for (var s in i)
        t[s] && "object" == typeof t[s] && "[object Object]" == t[s].toString() && i[s] ? extend(t[s], i[s]) : t[s] = i[s];
    return t
}
,
cad.jsapi = {
    CreateClient: function(t, i, s, e) {
        var n = null
          , a = !!e.cross_domain
          , h = e.origin ? e.origin : ""
          , r = {
            service: i,
            cross_domain: a,
            origin: h
        }
          , o = null
          , c = !1
          , l = function(s) {
            s.options.cross_domain = a,
            n = new cad.Client(t,s.guid,i,s.layout,s.options),
            t.cad.face = new cad.Face(n),
            o && o(n, s)
        };
        if ("function" == typeof e.async_mode ? (c = !0,
        o = e.async_mode) : c = !!e.async_mode,
        t && t.cad && t.cad.CloseClient(),
        "object" == typeof s) {
            var d = new FormData;
            d.append("file", s);
            var u = {
                type: "POST",
                url: i + "/jsapiopen?" + cad.makeParams({
                    file_name: ""
                }, r),
                data: d,
                processData: !1,
                contentType: !1,
                crossDomain: a,
                dataType: "json",
                async: c,
                success: function(t) {
                    l(t)
                },
                error: function(t) {
                    console.log(t)
                }
            };
            cad.setupAjax(r, u),
            $.ajax(u)
        } else
            cad.get(r, ["jsapiopen", {
                file_name: s
            }], void 0, function(t) {
                l(t)
            }, void 0, c);
        return n
    }
},
cad.SimpleBox = function() {
    this.saved = null,
    this.x = 0,
    this.y = 0,
    this.h = 0,
    this.w = 0,
    this.z = 1
}
,
cad.SimpleBox.prototype = {
    zoom: function(t, i) {
        if (this.h *= t,
        this.w *= t,
        this.z *= t,
        this.center && this.parent && this.parent.drawMatrix) {
            i || (i = this.parent.center);
            var s = {
                x: t,
                y: t,
                z: t
            }
              , e = this.center
              , n = cad.math.transformPoint(e, this.parent.drawMatrix)
              , a = cad.math.matScale(this.parent.drawMatrix, s)
              , h = cad.math.subPoint(n, {
                x: i.x,
                y: i.y,
                z: 0
            })
              , r = cad.math.distanceFVector(h);
            h = cad.math.ort(h);
            var o = cad.math.affineTransformPoint(e, a);
            o = cad.math.subPoint(n, o),
            o = cad.math.subPoint(o, cad.math.pointXScalar(h, r * (1 - t))),
            a[3] = [o.x, o.y, o.z, 0],
            this.parent.orbit.scale = cad.math.pointXScalar(this.parent.orbit.scale, t)
        }
    },
    reset: function() {
        this.x = 0,
        this.y = 0,
        this.z = 1
    },
    save: function() {
        this.saved = {
            x: this.x,
            y: this.y,
            w: this.w,
            h: this.h,
            z: this.z
        }
    },
    assign: function(t) {
        t.x && (this.x = t.x),
        t.y && (this.y = t.y),
        t.w && (this.w = t.w),
        t.h && (this.h = t.h),
        t.z && (this.z = t.z),
        t.extents && (this.extents = t.extents)
    }
},
cad.Box = function(t) {
    t && (this.parent = t,
    this.pic = {
        x: 0,
        y: 0,
        h: 0,
        w: 0,
        saved: null
    },
    this.pic.save = this.save,
    this.pic.zoom = this.zoom,
    this.pic.reset = function() {
        this.x = 0,
        this.y = 0,
        this.parent && this.parent.img && (this.w = this.parent.img.width,
        this.h = this.parent.img.height)
    }
    ,
    this.pic.parent = t,
    this.first = new cad.Box,
    this.first.parent = t,
    this.first.set = !0)
}
,
cad.Box.prototype = new cad.SimpleBox,
cad.extend(cad.Box.prototype, {
    setMatrixes: function(t) {
        return !!t && (this.parent.drawMatrix = cad.utils.copyArray(t),
        this.parent.orbit.rotMatrix = cad.utils.copyArray(t),
        this.parent.orbit.scale = cad.math.normalizeMatrix(this.parent.orbit.rotMatrix),
        !0)
    },
    init: function(t, i) {
        var s = {};
        s.id = this.parent.guid,
        s.l = this.parent.state.layout,
        this.parent.guid && cad.get(this.parent, ["layout", s], void 0, function(e) {
            (s = {}).id = this.parent.guid,
            s.h = this.parent.canvas.height,
            s.w = this.parent.canvas.width,
            cad.get(this.parent, ["extents", s], void 0, function(s) {
                var e = this.x
                  , n = this.y
                  , a = this.w
                  , h = this.h;
                if (s) {
                    this.extents = s,
                    this.box2 = s,
                    this.x = s.x,
                    this.y = s.y,
                    this.h = s.h,
                    this.w = s.w,
                    this.z = 1;
                    var r = {
                        x: (s.left + s.right) / 2,
                        y: (s.top + s.bottom) / 2,
                        z: (s.z1 + s.z2) / 2
                    };
                    this.center = r,
                    this.first.set && (this.first.set = !1,
                    this.first.x = s.x,
                    this.first.y = s.y,
                    this.first.h = s.h,
                    this.first.w = s.w,
                    this.first.z = 1,
                    this.first.fit()),
                    0 == s.x && 0 == s.y && (t ? (this.zoom(t),
                    i && (this.x = e + (a - this.w) / 2,
                    this.y = n + (h - this.h) / 2)) : this.fit()),
                    this.setMatrixes(s.matrix)
                }
            }
            .bind(this), null, !1)
        }
        .bind(this), null, !1)
    },
    fit: function() {
        var t = this.parent.canvas
          , i = this.w / this.h
          , s = t.width / t.height
          , e = 1;
        s > i ? e = t.height / this.h : s < i && (e = t.width / this.w),
        this.reset(),
        this.zoom(e)
    },
    sync: function(t) {
        this.syncm(),
        this.x += this.pic.x,
        this.y += this.pic.y,
        this.pic.reset()
    },
    syncm: function() {
        var t = this.parent.state;
        this.x += t.shift.mouse.x,
        this.y += t.shift.mouse.y,
        t.shift.reset()
    },
    pos: function() {
        return {
            x: this.x + this.pic.x,
            y: this.y + this.pic.y
        }
    }
}),
cad.PrintArea = function(t, i, s, e, n, a, h) {
    this.parent = t,
    this.asPdf = e,
    this.page = {},
    cad.extend(this.page, s);
    var r = null
      , o = null
      , c = null
      , l = null;
    if (h) {
        var d = i.x + i.w / 2
          , u = i.y + i.h / 2
          , x = 2 * s.margin
          , y = (s.h - x) / (s.w - x)
          , f = i.w
          , p = i.h
          , m = t.box.extents;
        if (r = this.parent.state.toCAD(i.x, i.y),
        c = this.parent.state.toCAD(i.x + i.w, i.y + i.h),
        r.x <= m.left || c.x >= m.right || r.y >= m.top || c.y <= m.bottom) {
            var g = t.box.extents.h / t.box.extents.w;
            i.h < i.w ? (f = i.h / g,
            p = i.h) : (f = i.w,
            p = i.w * g)
        }
        var v = null
          , w = null;
        y < 1 ? (v = .5 * p / y,
        w = .5 * p) : (v = .5 * f,
        w = .5 * f * y),
        r = this.parent.state.toCAD(d - v, u - w),
        o = this.parent.state.toCAD(d + v, u - w),
        c = this.parent.state.toCAD(d + v, u + w),
        l = this.parent.state.toCAD(d - v, u + w)
    } else {
        var b = Math.max(Math.max(t.drawMatrix[0][0], t.drawMatrix[1][1]), t.drawMatrix[2][2])
          , z = {
            x: i.w * b,
            y: -i.h * b,
            z: 0
        }
          , M = (t.canvas.width - z.x) / 2
          , C = (t.canvas.height - z.y) / 2;
        r = this.parent.state.toCAD(M, C),
        o = this.parent.state.toCAD(M + z.x, C),
        c = this.parent.state.toCAD(M + z.x, C + z.y),
        l = this.parent.state.toCAD(M, C + z.y)
    }
    this.dir_diag = cad.math.dirFVector(r, c),
    this.boxCAD = {
        x: r.x,
        y: r.y,
        z: r.z,
        w: cad.math.distanceFPoint(r, o),
        h: cad.math.distanceFPoint(r, l)
    },
    this.txt = n,
    this.options = JSON.parse(JSON.stringify(a))
}
,
cad.PrintArea.prototype = {
    getBottomRight: function() {
        var t = Math.sqrt(this.boxCAD.w * this.boxCAD.w + this.boxCAD.h * this.boxCAD.h);
        return this.parent.state.fromCAD(cad.math.pointOnLine(this.boxCAD, this.dir_diag, t))
    },
    getLeftTop: function() {
        return this.parent.state.fromCAD(this.boxCAD)
    },
    isPrint: function() {
        var t = !1;
        return this.boxCAD && this.boxCAD.x && (t = !0),
        t
    },
    moveTo: function(t, i) {
        var s = this.parent.state.toCAD(t, i);
        this.boxCAD.x = s.x,
        this.boxCAD.y = s.y,
        this.boxCAD.z = s.z
    },
    reset: function() {},
    rect: function() {
        var t = this.getLeftTop()
          , i = this.getBottomRight();
        return {
            x: t.x,
            y: t.y,
            w: Math.abs(i.x - t.x),
            h: Math.abs(i.y - t.y)
        }
    },
    pass: function(t, i) {
        var s = this.rect();
        return t > s.x && i > s.y && t < s.w + s.x && i < s.h + s.y
    },
    save: function() {
        this.savedCAD = {
            x: this.boxCAD.x,
            y: this.boxCAD.y,
            z: this.boxCAD.z,
            h: this.boxCAD.h,
            w: this.boxCAD.w
        }
    },
    savedPos: function() {
        return this.parent.state.fromCAD(this.savedCAD)
    }
},
cad.WebClientEntity = function(t) {
    this.color = t
}
,
cad.WebClientEntity.prototype = {
    draw: function(t, i) {
        i.save();
        try {
            i.strokeStyle = this.color,
            i.fillStyle = this.color,
            this.strokeStyle && (i.strokeStyle = this.strokeStyle),
            this.fillStyle && (i.fillStyle = this.fillStyle),
            this.drawInternal(t, i)
        } finally {
            i.restore()
        }
    }
},
cad.WebClientPolyline = function(t, i) {
    cad.WebClientEntity.bind(this)(i),
    this.PolyPoints = t
}
,
cad.WebClientPolyline.prototype = new cad.WebClientEntity,
cad.extend(cad.WebClientPolyline.prototype, {
    drawInternal: function(t, i) {
        i.beginPath(),
        cad.draw.drawPolyline(this.PolyPoints, t, i),
        i.stroke()
    }
}),
cad.WebClientPolygon = function(t, i, s) {
    cad.WebClientPolyline.bind(this)(t, i),
    this.fillStyle = s
}
,
cad.WebClientPolygon.prototype = new cad.WebClientPolyline,
cad.extend(cad.WebClientPolygon.prototype, {
    drawInternal: function(t, i) {
        i.beginPath(),
        cad.draw.drawPolyline(this.PolyPoints, t, i),
        i.stroke(),
        i.fill()
    }
}),
cad.WebClientText = function(t, i, s, e, n, a, h, r, o) {
    cad.WebClientEntity.bind(this)(r),
    this.Point = i,
    this.Point1 = s,
    this.HAlign = n,
    this.VAlign = a,
    this.Rotation = e,
    this.font = h,
    this.Text = t,
    this.backgroundColor = o
}
,
cad.WebClientText.prototype = new cad.WebClientEntity,
cad.extend(cad.WebClientText.prototype, {
    drawInternal: function(t, i) {
        var s = null;
        i.beginPath(),
        s = 0 == this.HAlign && 0 == this.VAlign || 3 == this.HAlign ? this.Point : this.Point1,
        cad.draw.setTextParams(s, this.HAlign, this.VAlign, this.Rotation, this.font, t, i);
        var e = cad.draw.getTextRect(this.Text, i)
          , n = i.fillStyle;
        try {
            i.fillStyle = this.backgroundColor,
            i.fillRect(e.x, e.y, e.width, e.height)
        } finally {
            i.fillStyle = n,
            i.fillText(this.Text, 0, 0)
        }
        i.stroke()
    }
}),
cad.WebClientMeasureTool = function(t, i, s, e, n) {
    cad.WebClientEntity.bind(this)(n),
    this.textEnt = t,
    this.polyEnt = i,
    this.left_arrowEnt = s,
    this.right_arrowEnt = e
}
,
cad.WebClientMeasureTool.prototype = new cad.WebClientEntity,
cad.extend(cad.WebClientMeasureTool.prototype, {
    drawInternal: function(t, i) {
        this.polyEnt && this.polyEnt.draw(t, i),
        this.left_arrowEnt && this.left_arrowEnt.draw(t, i),
        this.right_arrowEnt && this.right_arrowEnt.draw(t, i),
        this.textEnt && this.textEnt.draw(t, i)
    }
}),
cad.createLengthDimension = function(t, i, s, e) {
    if (t.length < 2)
        return null;
    var n = i.strokeStyle
      , a = i.font
      , h = 1
      , r = ""
      , o = e.arrowScale * s.cad_w
      , c = e.arrowScale * s.cad_h;
    e && (h = e.CADsInUnit,
    r = e.ScaleName);
    var l = cad.geom.getCenterOfPolyline(t)
      , d = cad.geom.calcAngle(l.point1, l.point2)
      , u = cad.geom.calcDist(t) / h
      , x = Math.floor(d.angle / 90)
      , y = 1 == x || 2 == x ? 1 : -1
      , f = new cad.WebClientPolyline(t,n)
      , p = new cad.WebClientPolyline(cad.geom.createArrow(t, o, c, !0),n)
      , m = new cad.WebClientPolyline(cad.geom.createArrow(t, o, c, !1),n)
      , g = new cad.WebClientText(u.toFixed(2) + " " + r,l.center,cad.geom.getOrtPoint(l.point1, l.center, -y * c),t.length > 2 ? 0 : d.angle - 90 * (1 + y),1,5,cad.draw.getCADFont(a, s),n,i.textBackgroundStyle);
    return new cad.WebClientMeasureTool(g,f,p,m,n)
}
,
cad.createAreaDimension = function(t, i, s, e) {
    if (t.length < 3)
        return null;
    var n = i.strokeStyle
      , a = i.font
      , h = i.fillStyle
      , r = 1
      , o = ""
      , c = (e.arrowScale,
    s.cad_w,
    e.arrowScale * s.cad_h);
    e && (r = e.CADsInUnit,
    o = e.ScaleName);
    var l = cad.geom.getCenterOfBox(cad.geom.getPolylineBox(t))
      , d = cad.geom.calcArea(t) / (r * r)
      , u = new cad.WebClientPolygon(t,n,h)
      , x = new cad.WebClientText(d.toFixed(2) + " " + o + "2",{
        x: l.x,
        y: l.y - c,
        z: 0
    },l,0,1,5,cad.draw.getCADFont(a, s),n,i.textBackgroundStyle);
    return new cad.WebClientMeasureTool(x,u,null,null,n)
}
,
cad.Measuring = function(t) {
    this.parent = t,
    this.points = [],
    this.lastPoints = [],
    this.mode = 0,
    this.isClearPointList = !1,
    this.cursorStyle = t.canvas.style.cursor,
    this.lastSegments = [],
    this.font = {
        style: t.settings.measureTextStyle,
        height: 14
    },
    this.strokeStyle = "magenta",
    this.fillStyle = "rgba(255,0,255,0.2)",
    this.textBackgroundStyle = "rgba(255,255,255,0.2)",
    this.count = null,
    this.isClearMeasuring = !0,
    this.isPolarMeasuring = !1,
    this.polarAngle = 90,
    this.startMetrix = null
}
,
cad.Measuring.prototype = {
    translatePoint: function(t) {
        if (this.parent.state.snapPoint)
            return t;
        if (!this.isPolarMeasuring)
            return t;
        if (!this.count || "number" != typeof this.count)
            return t;
        try {
            if (0 == this.polarAngle || this.count < 1)
                return t;
            if (t) {
                var i = this.points[this.count - 1]
                  , s = cad.geom.calcAngle(i, t)
                  , e = Math.round(s.angle / this.polarAngle) * this.polarAngle * Math.PI / 180
                  , n = Math.sqrt(s.deltaX * s.deltaX + s.deltaY * s.deltaY);
                return {
                    x: i.x + n * Math.cos(e),
                    y: i.y + n * Math.sin(e),
                    z: 0
                }
            }
            return t
        } catch (i) {
            return t
        }
    },
    doShowMeasuring: function(t, i, s, e, n, a, h) {
        var r = [];
        try {
            this.lastSegments.forEach(function(t) {
                var i = 0
                  , s = t.polyEnt;
                i = s instanceof cad.WebClientPolygon ? 3 : 2 == s.PolyPoints.length ? 1 : 2,
                r.push({
                    is_complete: !0,
                    mode: i,
                    points: s.PolyPoints
                })
            }),
            this.points && this.points.length > 1 && r.push({
                is_complete: !1,
                mode: this.mode,
                points: this.points
            })
        } finally {
            this.onShowMeasuring && "function" == typeof this.onShowMeasuring && this.onShowMeasuring(t, i, s, e, n, a, h, r)
        }
    },
    start: function(t) {
        t >= 1 && t <= 3 && this.onmeasuring && "function" == typeof this.onmeasuring && !this.onmeasuring(this.parent) || (this.doShowMeasuring(!1, !1, !1, !1, !1, !1, !1),
        this.points = [],
        this.lastPoints = [],
        this.isClearMeasuring ? this.lastSegments = [] : t || (this.lastSegments = []),
        this.mode = t,
        this.isClearPointList = !1,
        this.isRestartMode1 = !1,
        this.parent.canvas.style.cursor = this.cursorStyle,
        this.count = null,
        this.startMetrix && 0 != this.lastSegments.length || (this.startMetrix = this.parent.screenMetrix()))
    },
    set: function(t, i, s, e) {
        this.setCAD(this.parent.state.toCAD(t, i), s, e)
    },
    setCAD: function(t, i, s) {
        if (this.mode) {
            if (this.isClearPointList && (this.points = [],
            this.isClearPointList = !1),
            this.isRestartMode1 && (this.lastSegments = [],
            this.isRestartMode1 = !1),
            1 == this.mode && s)
                this.points = this.lastPoints,
                this.isClearPointList = !0,
                this.lastPoints = [];
            else if (!s) {
                var e = this.translatePoint(t);
                this.points.length > 0 ? this.points[this.count] = e : this.points.push(e),
                this.count = this.points.length
            }
            if ((this.mode < 2 ? this.points.length > 1 : i > 0) || s && 0 == i) {
                var n = {
                    font: this.font,
                    strokeStyle: this.strokeStyle,
                    fillStyle: this.fillStyle,
                    textBackgroundStyle: this.textBackgroundStyle
                };
                if (this.isClearPointList = !0,
                this.lastPoints = this.points,
                1 == this.mode && (s ? this.isRestartMode1 = !1 : this.lastSegments.push(cad.createLengthDimension([this.points[0], this.points[1]], n, this.startMetrix, this.parent.settings))),
                s && 0 == i && this.points.length > 2) {
                    switch (this.points.splice(-1, 1),
                    this.mode) {
                    case 2:
                        this.lastSegments.push(cad.createLengthDimension(this.points, n, this.startMetrix, this.parent.settings));
                        break;
                    case 3:
                        this.points.push(this.points[0]),
                        this.lastSegments.push(cad.createAreaDimension(this.points, n, this.startMetrix, this.parent.settings))
                    }
                    this.lastPoints = [],
                    this.points = []
                }
                (1 != this.mode || i > 0) && !s ? this.doShowMeasuring(!1, !1, !1, !1, !1, !1, !1) : s && this.doShowMeasuring(!1, !1, !1, !1, !1, !1, !0)
            }
            this.parent.canvas.style.cursor = "default"
        }
    },
    draw: function(t, i, s) {
        var e = {
            x: i,
            y: s
        }
          , n = {
            x: i,
            y: s
        };
        if (this.mode && (this.points.length > 0 || this.lastSegments.length > 0))
            if (this.points.length > 0 && !this.isClearPointList && (e = this.parent.state.fromCAD(this.points[0]),
            n = null,
            lpCAD = null,
            this.count || (this.count = this.points.length),
            this.count > this.points.length && (this.count = this.points.length),
            this.parent.state.snapPoint ? lpCAD = this.parent.state.snapPoint : arguments.length > 2 ? lpCAD = this.parent.state.toCAD(i, s) : lpCAD = this.points[this.points.length - 1],
            lpCAD = this.translatePoint(lpCAD),
            lpCAD ? (n = this.parent.state.fromCAD(lpCAD),
            this.points[this.count] = lpCAD) : arguments.length > 2 && (lpCAD = this.translatePoint(this.parent.state.toCAD(i, s)),
            lpCAD && (n = this.parent.state.fromCAD(lpCAD),
            this.points[this.count] = lpCAD))),
            t) {
                var a = function(t) {
                    return cad.geom.calcDist(t)
                }
                .bind(this)
                  , h = function(t) {
                    return cad.geom.calcArea(t)
                }
                .bind(this)
                  , r = function(t) {
                    var i = {
                        angle: !1,
                        deltaX: !1,
                        deltaY: !1
                    };
                    if (t && t.length >= 2) {
                        var s = t[t.length - 2]
                          , e = t[t.length - 1];
                        s && e && (i = cad.geom.calcAngle(s, e))
                    }
                    return i
                }
                .bind(this);
                t.strokeStyle = this.strokeStyle,
                t.fillStyle = this.fillStyle,
                t.font = cad.draw.getFullFontName(this.font),
                t.lineWidth = 2,
                this.points.length > 0 && !this.isClearPointList && (t.beginPath(),
                t.arc(e.x, e.y, 3, 0, 2 * Math.PI),
                t.arc(n.x, n.y, 3, 0, 2 * Math.PI),
                t.fill());
                var o = function(t) {
                    return this.parent.state.fromCAD(t)
                }
                .bind(this);
                t.beginPath();
                try {
                    this.lastSegments.forEach(function(i) {
                        i && i.draw(o, t)
                    }),
                    this.points.length > 1 && (this.parent.drawPolyline(this.points, o, t),
                    3 == this.mode && this.points.length > 2 && (t.fillStyle = this.fillStyle,
                    t.fill(),
                    t.fillStyle = this.strokeStyle))
                } finally {
                    t.stroke()
                }
                var c = r(this.points);
                if (3 == this.mode) {
                    var l = h(this.points)
                      , d = a(this.points);
                    this.doShowMeasuring(!1, l, d, c.angle, c.deltaX, c.deltaY, !0)
                } else if (2 == this.mode) {
                    d = a(this.points);
                    this.doShowMeasuring(!1, !1, d, c.angle, c.deltaX, c.deltaY, !0)
                } else {
                    d = a(this.points);
                    this.doShowMeasuring(d, !1, !1, c.angle, c.deltaX, c.deltaY, !0)
                }
            } else
                this.doShowMeasuring(!1, !1, !1, !1, !1, !1)
    }
},
cad.Orbit = function(t) {
    this.parent = t,
    this.cursorStyle = t.canvas.style.cursor,
    this.enabled = !1,
    this.state = 0,
    this.init()
}
,
cad.Orbit.prototype = {
    init: function(t, i) {
        this.rotMatrix || (this.rotMatrix = cad.math.initMatrix()),
        this.moving = !!i,
        this.center = {
            x: this.parent.canvas.width / 2,
            y: this.parent.canvas.height / 2,
            z: 0
        },
        this.radius = this.center.y / 1.5,
        t && (this.screenRotCenter = cad.math.transformPoint(this.parent.box.center, this.parent.drawMatrix)),
        i && this.setCursorState(i.x, i.y),
        this.lastp = i
    },
    smallCenters: function(t) {
        var i = function(t, i) {
            return (1 & t) * (((2 & t) >> 1 << 1) - 1) * i
        };
        return {
            x: this.center.x + i(t - 1, this.radius),
            y: this.center.y + i(t, this.radius)
        }
    },
    setCursorState: function(t, i) {
        this.center = {
            x: this.parent.canvas.width / 2,
            y: this.parent.canvas.height / 2,
            z: 0
        },
        this.radius = this.center.y / 1.5,
        this.currentState = 0;
        for (var s = 0; s <= 3 && !cad.math.isPointInCircle(this.smallCenters(s), 20, t, i); )
            s++;
        s <= 3 ? this.currentState = 1 & s ^ 1 : cad.math.isPointInCircle(this.center, this.radius, t, i) ? this.currentState = 3 : this.currentState = 2
    },
    drawBox: function(t, i, s) {
        t.lineWidth = 3,
        t.strokeStyle = i,
        t.beginPath(),
        t.moveTo(s[0].x, s[0].y),
        t.lineTo(s[1].x, s[1].y),
        t.lineTo(s[2].x, s[2].y),
        t.lineTo(s[3].x, s[3].y),
        t.lineTo(s[0].x, s[0].y),
        s.length > 4 && (t.moveTo(s[4].x, s[4].y),
        t.lineTo(s[5].x, s[5].y),
        t.lineTo(s[6].x, s[6].y),
        t.lineTo(s[7].x, s[7].y),
        t.lineTo(s[4].x, s[4].y),
        t.lineTo(s[0].x, s[0].y),
        t.moveTo(s[1].x, s[1].y),
        t.lineTo(s[5].x, s[5].y),
        t.moveTo(s[2].x, s[2].y),
        t.lineTo(s[6].x, s[6].y),
        t.moveTo(s[3].x, s[3].y),
        t.lineTo(s[7].x, s[7].y)),
        t.stroke()
    },
    transformPoints: function(t, i) {
        var s;
        for (s = 0; s < t.length; s++)
            t[s] = cad.math.transformPoint(t[s], i)
    },
    draw: function(t) {
        if (0 == this.parent.state.layout) {
            if (t && this.enabled)
                if (this.center = {
                    x: this.parent.canvas.width / 2,
                    y: this.parent.canvas.height / 2,
                    z: 0
                },
                this.radius = this.center.y / 1.5,
                this.moving) {
                    var i = cad.utils.copyArray(this.rotMatrix);
                    cad.math.matScale(i, {
                        x: 1,
                        y: -1,
                        z: 1
                    }),
                    i[3][0] = this.center.x,
                    i[3][1] = this.center.y,
                    i[3][2] = this.center.z;
                    this.parent.box.first.h,
                    this.parent.box.first.w;
                    var s = this.parent.box.box2
                      , e = Math.abs(s.z1 - s.z2) <= .001;
                    if (e)
                        var n = [{
                            x: -this.radius,
                            y: -this.radius,
                            z: 0
                        }, {
                            x: this.radius,
                            y: -this.radius,
                            z: 0
                        }, {
                            x: this.radius,
                            y: this.radius,
                            z: 0
                        }, {
                            x: -this.radius,
                            y: this.radius,
                            z: 0
                        }];
                    else
                        n = [{
                            x: -this.radius,
                            y: -this.radius,
                            z: -this.radius
                        }, {
                            x: this.radius,
                            y: -this.radius,
                            z: -this.radius
                        }, {
                            x: this.radius,
                            y: this.radius,
                            z: -this.radius
                        }, {
                            x: -this.radius,
                            y: this.radius,
                            z: -this.radius
                        }, {
                            x: -this.radius,
                            y: -this.radius,
                            z: this.radius
                        }, {
                            x: this.radius,
                            y: -this.radius,
                            z: this.radius
                        }, {
                            x: this.radius,
                            y: this.radius,
                            z: this.radius
                        }, {
                            x: -this.radius,
                            y: this.radius,
                            z: this.radius
                        }];
                    this.transformPoints(n, i),
                    this.drawBox(t, "blue", n);
                    i = this.calcDrawMatrix();
                    if (e)
                        n = [{
                            x: s.left,
                            y: s.top,
                            z: s.z1
                        }, {
                            x: s.right,
                            y: s.top,
                            z: s.z1
                        }, {
                            x: s.right,
                            y: s.bottom,
                            z: s.z1
                        }, {
                            x: s.left,
                            y: s.bottom,
                            z: s.z1
                        }];
                    else
                        n = [{
                            x: s.left,
                            y: s.top,
                            z: s.z1
                        }, {
                            x: s.right,
                            y: s.top,
                            z: s.z1
                        }, {
                            x: s.right,
                            y: s.bottom,
                            z: s.z1
                        }, {
                            x: s.left,
                            y: s.bottom,
                            z: s.z1
                        }, {
                            x: s.left,
                            y: s.top,
                            z: s.z2
                        }, {
                            x: s.right,
                            y: s.top,
                            z: s.z2
                        }, {
                            x: s.right,
                            y: s.bottom,
                            z: s.z2
                        }, {
                            x: s.left,
                            y: s.bottom,
                            z: s.z2
                        }];
                    this.transformPoints(n, i),
                    this.drawBox(t, "green", n)
                } else {
                    t.lineWidth = 1,
                    t.strokeStyle = "green",
                    t.fillStyle = "green",
                    t.save();
                    try {
                        t.beginPath(),
                        t.arc(this.center.x, this.center.y, this.radius, 0, 2 * Math.PI),
                        t.stroke(),
                        t.beginPath(),
                        t.arc(this.center.x, this.center.y + this.radius, 10, 0, 2 * Math.PI),
                        t.stroke(),
                        t.beginPath(),
                        t.arc(this.center.x, this.center.y - this.radius, 10, 0, 2 * Math.PI),
                        t.stroke(),
                        t.beginPath(),
                        t.arc(this.center.x + this.radius, this.center.y, 10, 0, 2 * Math.PI),
                        t.stroke(),
                        t.beginPath(),
                        t.arc(this.center.x - this.radius, this.center.y, 10, 0, 2 * Math.PI),
                        t.stroke()
                    } finally {
                        t.restore()
                    }
                }
        } else
            this.enabled = !1
    },
    rotate: function(t, i) {
        0 != i && (this.rotMatrix = cad.math.multiplyMatrix(cad.math.buildRotMatrix(t, i), this.rotMatrix))
    },
    rotateDirectly: function(t, i, s) {
        switch (this.currentState) {
        case 0:
            this.rotate(1, i);
            break;
        case 1:
            this.rotate(2, t);
            break;
        case 2:
            Math.abs(t) > Math.abs(i) ? this.rotate(3, (2 * (s.y >= this.center.y ? 1 : 0) - 1) * t) : this.rotate(3, (2 * (s.x < this.center.x ? 1 : 0) - 1) * i);
            break;
        case 3:
            this.rotate(1, i),
            this.rotate(2, t)
        }
    },
    move: function(t, i) {
        var s = i.x - this.lastp.x
          , e = i.y - this.lastp.y;
        this.rotateDirectly(s, e, i),
        this.lastp = i
    },
    calcDrawMatrix: function() {
        var t = cad.utils.copyArray(this.rotMatrix);
        cad.math.matScale(t, {
            x: this.scale.x,
            y: -this.scale.y,
            z: this.scale.z
        });
        var i = cad.math.subPoint(this.screenRotCenter, cad.math.affineTransformPoint(this.parent.box.center, t));
        return t[3] = [i.x, i.y, i.z, 1],
        t
    },
    finish: function(t) {
        this.parent.drawMatrix = this.calcDrawMatrix(),
        this.parent.refresh(),
        this.moving = !1
    }
},
cad.Shift = function() {
    this.reset()
}
,
cad.Shift.prototype = {
    reset: function() {
        this.coef = 1,
        this.zoom = {
            x: 0,
            y: 0
        },
        this.mouse = {
            x: 0,
            y: 0
        },
        this.touch = {
            x: 0,
            y: 0
        }
    },
    x: function() {
        return this.zoom.x + this.mouse.x - this.touch.x
    },
    y: function() {
        return this.zoom.y + this.mouse.y - this.touch.y
    },
    empty: function() {
        return 0 === this.x() && 0 === this.y()
    }
},
cad.State = function(t) {
    this.parent = t,
    this.reset()
}
,
cad.State.prototype = {
    reset: function() {
        this.move = !1,
        this.touchmove = !1,
        this.infoEnabled = !1,
        this.first = null,
        this.shift = new cad.Shift,
        this.layout = 0,
        this.mode = 0,
        this.snapPoint = null,
        this.snapType = null
    },
    x: function() {
        return this.first.x - this.shift.x()
    },
    y: function() {
        return this.first.y - this.shift.y()
    },
    getParam: function(t) {
        t.l = this.layout,
        t.mode = this.mode
    },
    clipRect: function(t) {
        var i = cad.math.transformPoint({
            x: this.parent.box.extents.left,
            y: this.parent.box.extents.top,
            z: 0
        }, this.parent.drawMatrix)
          , s = cad.math.transformPoint({
            x: this.parent.box.extents.right,
            y: this.parent.box.extents.bottom,
            z: 0
        }, this.parent.drawMatrix)
          , e = this.parent.box.extents.w / (s.x - i.x)
          , n = this.parent.box.extents.h / (s.y - i.y)
          , a = {};
        return a.x = (t.x - i.x) * e,
        a.y = (t.y - i.y) * n,
        a.w = t.w * e,
        a.h = t.h * n,
        a
    },
    fromCAD: function(t, i, s) {
        var e = null;
        return e = t && Number.isFinite(t.x) && Number.isFinite(t.y) && Number.isFinite(t.z) ? t : t && Number.isFinite(t.x) && Number.isFinite(t.y) ? {
            x: t.x,
            y: t.y,
            z: 0
        } : Number.isFinite(t) && Number.isFinite(i) && Number.isFinite(s) ? {
            x: t,
            y: i,
            z: s
        } : {
            x: t,
            y: i,
            z: 0
        },
        cad.math.transformPoint(e, this.parent.drawMatrix)
    },
    toCAD: function(t, i) {
        return cad.math.screenToDC({
            x: t,
            y: i
        }, this.parent.drawMatrix)
    },
    setInfoEnabled: function() {
        this.infoEnabled = !this.infoEnabled
    }
},
cad.Settings = function(t) {
    this.parent = t,
    this.noscroll = !1,
    this.nomsg = !1,
    this.black = !1,
    this.ScaleName = "m",
    this.CADsInUnit = 1,
    this.showBrowserMenu = !1,
    this.measureTextStyle = {
        styleName: cad.cDefaultMeasureTextStyleName,
        styleFont: cad.cDefaultFontName
    },
    this.arrowScale = .007,
    this.isUsePostMethodForPdfPrint = !1
}
,
cad.Settings.prototype = {
    init: function() {
        this.parent.guid
    }
},
cad.Client = function(t, i, s, e, n) {
    if (this.serverOptions = n,
    this.cross_domain = !(!n || !n.cross_domain),
    this.origin = n.origin,
    this.onBeginDraw = this.beginDraw.bind(this),
    this.onEndDraw = this.endDraw.bind(this),
    this.onMoveEvent = this.moveEvent.bind(this),
    this.onMouseWhell = this.mouseWhell.bind(this),
    this.onDoubleClick = this.doubleClick.bind(this),
    this.onResizeEvent = this.resizeEvent.bind(this),
    this.onCancelDrawEvent = this.cancelDrawEvent.bind(this),
    this.onContextMenu = function(t) {
        t.preventDefault(),
        t.returnValue = this.settings.showBrowserMenu,
        this.oncontextmenu && this.oncontextmenu(t)
    }
    .bind(this),
    "string" == typeof t && (t = document.getElementById(t)),
    t) {
        if (!t.getContext)
            throw new TypeError("Unsupported browser: getContext not found");
        t.cad = this,
        t.init = function() {
            t.height = t.clientHeight,
            t.width = t.clientWidth,
            t.cad.center = {
                x: t.width / 2,
                y: t.height / 2,
                z: 0
            }
        }
        ,
        this.ctx = t.getContext("2d")
    }
    this.canvas = t,
    this.box = new cad.Box(this),
    this.state = new cad.State(this),
    this.settings = new cad.Settings(this),
    this.service = s || cad.service,
    this.state.layout = e || 0,
    this.measuring = new cad.Measuring(this),
    this.orbit = new cad.Orbit(this),
    this.init(i),
    n && n.saveMatrix ? (this.box.init(),
    this.setMatrixes(n.saveMatrix, !0)) : this.fitToWindow(),
    this.cursorStyle = t.style.cursor,
    this.isShowSnap = !1,
    this.printIcon = null
}
,
cad.Client.prototype = {
    screenMetrix: function() {
        var t = this.state.toCAD(0, 0)
          , i = this.state.toCAD(this.canvas.width, this.canvas.height)
          , s = cad.math.subPoint(i, t)
          , e = (s = {
            x: Math.abs(s.x),
            y: Math.abs(s.y)
        }).x / this.canvas.width
          , n = s.y / this.canvas.height;
        return {
            screen_w: this.canvas.width,
            screen_h: this.canvas.height,
            cad_w: s.x,
            cad_h: s.y,
            pix_w: e,
            pix_h: n
        }
    },
    init: function(t) {
        this.canvas && (this.canvas.init(),
        this.canvas.addEventListener("mousedown", this.onBeginDraw),
        this.canvas.addEventListener("touchstart", this.onBeginDraw),
        this.canvas.addEventListener("mouseup", this.onEndDraw),
        this.canvas.addEventListener("touchend", this.onEndDraw),
        this.canvas.addEventListener("mouseout", this.onEndDraw),
        this.canvas.addEventListener("mousemove", this.onMoveEvent),
        this.canvas.addEventListener("touchmove", this.onMoveEvent),
        this.canvas.addEventListener("DOMMouseScroll", this.onMouseWhell),
        this.canvas.addEventListener("mousewheel", this.onMouseWhell),
        this.canvas.addEventListener("dblclick", this.onDoubleClick),
        window.addEventListener("resize", this.onResizeEvent),
        document.addEventListener("keydown", this.onCancelDrawEvent),
        this.canvas.addEventListener("contextmenu", this.onContextMenu),
        this.onerror = function(t) {
            alert(t)
        }
        ,
        this.guid = t,
        this.face && (this.face.guid = t))
    },
    CloseClient: function() {
        this.canvas.removeEventListener("mousedown", this.onBeginDraw),
        this.canvas.removeEventListener("touchstart", this.onBeginDraw),
        this.canvas.removeEventListener("mouseup", this.onEndDraw),
        this.canvas.removeEventListener("touchend", this.onEndDraw),
        this.canvas.removeEventListener("mouseout", this.onEndDraw),
        this.canvas.removeEventListener("mousemove", this.onMoveEvent),
        this.canvas.removeEventListener("touchmove", this.onMoveEvent),
        this.canvas.removeEventListener("DOMMouseScroll", this.onMouseWhell),
        this.canvas.removeEventListener("mousewheel", this.onMouseWhell),
        this.canvas.removeEventListener("dblclick", this.onDoubleClick),
        window.removeEventListener("resize", this.onResizeEvent),
        document.removeEventListener("keydown", this.onCancelDrawEvent),
        this.canvas.removeEventListener("contextmenu", this.onContextMenu)
    },
    makeParams: function(t, i) {
        return cad.makeParams(t, this)
    },
    getBackgroundColor: function() {
        return this.canvas && this.canvas.style && this.canvas.style.backgroundColor ? this.canvas.style.backgroundColor : "white"
    },
    getCursor: function() {
        return this.canvas.style.cursor
    },
    getDrawMatrix: function() {
        return this.drawMatrix
    },
    getExtents: function() {
        return this.box.extents
    },
    getForegroundColor: function() {
        return this.canvas && this.canvas.style && this.canvas.style.color ? this.canvas.style.color : "black"
    },
    setCursor: function(t) {
        this.cursorStyle = t,
        this.Orbit && (this.Orbit.cursorStyle = t),
        this.Measuring && (this.Measuring.cursorStyle = t),
        this.canvas && (this.canvas.style.cursor = t)
    },
    setMatrixes: function(t, i) {
        this.box.setMatrixes(t),
        i && this.refresh()
    },
    merge: function(t, i, s, e, n, a) {
        if ("undefined" != typeof FormData) {
            (r = new FormData).append("data", JSON.stringify({
                id: this.guid,
                name: i,
                pos: s,
                scale: e,
                rotation: n,
                color: cad.utils.colorToHex(a)
            })),
            r.append("file", t);
            var h = {
                type: "POST",
                url: this.service + "/mergeaction?rnd=" + Math.random(),
                data: r,
                processData: !1,
                contentType: !1,
                dataType: "json",
                async: !1,
                success: function(t) {
                    console.log(t)
                },
                error: function(t) {
                    console.log(t)
                }
            };
            cad.setupAjax(this, h),
            $.ajax(h)
        } else {
            var r = {
                data: JSON.stringify({
                    id: this.guid,
                    name: i,
                    pos: s,
                    scale: e,
                    rotation: n,
                    color: cad.utils.colorToHex(a)
                }),
                file: t
            };
            $.post(this.service + "/mergeaction?rnd=" + Math.random(), r)
        }
        cad.get(this, ["extents", {
            id: this.guid,
            h: this.canvas.height,
            w: this.canvas.width
        }], void 0, function(t) {
            this.box.extents = t,
            this.box.h = t.h * this.box.z,
            this.box.w = t.w * this.box.z,
            this.refresh()
        }
        .bind(this), null, !1)
    },
    findText: function(t, i) {
        var s = {};
        return cad.get(this, ["findtext", {
            id: this.guid,
            str: t,
            is_parital: i
        }], void 0, function(t) {
            s = t
        }
        .bind(this), null, !1),
        s
    },
    showLineWeight: function (i) {
        var e = null;
        return cad.get(this, ["lineweight", {
            id: this.guid,
            isShow: i
        }], void 0, (function(i) {
            e = i,
                t
        }))
    },
    getDrawMode: function() {
        return this.state.mode
    },
    setDrawMode: function(t) {
        this.state.mode = t,
        this.refresh()
    },
    processXML: function(t) {
        var i = "";
        return cad.get(this, ["xmlcmd", {
            id: this.guid,
            xml: t
        }], void 0, function(t) {
            i = t,
            this.refreshBuff()
        }
        .bind(this), null, !1),
        i
    },
    zoomToEntityByHandle: function(t) {
        var i = {};
        i.id = this.guid,
        i.handle = t,
        cad.get(this, ["zoomentity", i], void 0, function(t) {
            if (t.result > 0) {
                var i = function(t, i) {
                    var s = {
                        x: t,
                        y: i,
                        z: 0
                    };
                    return cad.math.transformPoint(s, this.drawMatrix)
                }
                .bind(this)
                  , s = i(t.box.Left, t.box.Top)
                  , e = i(t.box.Right, t.box.Bottom)
                  , n = e.x > s.x ? e.x - s.x : s.x - e.x
                  , a = e.y > s.y ? e.y - s.y : s.y - e.y
                  , h = n / a
                  , r = this.canvas.width / this.canvas.height
                  , o = 1;
                r > h ? o = this.canvas.height / a : r < h && (o = this.canvas.width / n);
                var c = {
                    x: this.canvas.width / 2,
                    y: this.canvas.height / 2
                }
                  , l = {
                    x: (s.x + e.x) / 2,
                    y: (s.y + e.y) / 2
                }
                  , d = {
                    x: c.x - l.x,
                    y: c.y - l.y
                };
                this.drawMatrix = cad.math.matTranslate(this.drawMatrix, d.x, d.y, 0),
                this.box.zoom(.8 * o),
                this.refresh()
            }
        }
        .bind(this), null, !1)
    },
    fitToWindow: function() {
        var t = {};
        t.id = this.guid,
        cad.get(this, ["fittowindow", t], void 0, function(t) {
            this.show(this.guid, !0)
        }
        .bind(this), null, !1)
    },
    copyToClipboard: function() {
        var t = document.createElement("DIV");
        document.body.appendChild(t);
        var i = document.createElement("IMG");
        t.appendChild(i),
        i.src = this.canvas.toDataURL("image/png");
        var s = document.createRange();
        s.setStart(i, 0),
        s.setEnd(i, 0),
        s.selectNode(i);
        var e = window.getSelection();
        return e.addRange(s),
        document.execCommand("Copy"),
        e.removeAllRanges(),
        document.body.removeChild(t),
        i
    },
    resizeEvent: function() {
        var t = {};
        t.id = this.guid,
        this.canvas.height = this.canvas.clientHeight,
        this.canvas.width = this.canvas.clientWidth,
        t.h = this.canvas.height,
        t.w = this.canvas.width,
        this.center = {
            x: this.canvas.width / 2,
            y: this.canvas.height / 2,
            z: 0
        },
        cad.get(this, ["initdrawing", t], void 0, function(t) {
            this.refresh()
        }
        .bind(this), null, !1)
    },
    cancel: function() {
        this.measuring.start(!1),
        this.orbit.enabled = !1,
        this.printArea = null,
        this.redraw(),
        this.measuring.draw(!1)
    },
    cancelDrawEvent: function(t) {
        27 == t.keyCode && this.cancel()
    },
    isPrint: function() {
        var t = !1;
        return this.printArea && this.printArea.isPrint() && (t = !0),
        t
    },
    doubleClick: function(t) {
        var i = this.getCoord(t);
        this.measuring.mode && (this.state.snapPoint ? this.measuring.setCAD(this.state.snapPoint, t.button, !0) : this.measuring.set(i.x, i.y, t.button, !0)),
        this.refresh()
    },
    getCoord: function(t) {
        var i = {
            x: 0,
            y: 0,
            touch: null
        };
        return t.type.indexOf("touch") > -1 ? t.changedTouches.length > 0 && (i.x = t.changedTouches[0].pageX,
        i.y = t.changedTouches[0].pageY,
        i.touch = t.touches) : (i.x = t.offsetX ? t.offsetX : t.layerX - t.target.offsetLeft,
        i.y = t.offsetY ? t.offsetY : t.layerY - t.target.offsetTop,
        (cad.utils.browser.firefox || cad.utils.browser.msie) && (this.canvas.style.width.indexOf("%") > 0 && (i.x = Math.round(i.x * (this.canvas.width / this.canvas.offsetWidth))),
        this.canvas.style.height.indexOf("%") > 0 && (i.y = Math.round(i.y * (this.canvas.height / this.canvas.offsetHeight))))),
        i
    },
    detectDoubleTap: function(t, i) {
        this.lastTap ? (cad.math.calcDist(this.lastTap, t) < 50 && (this.lastTap = !1,
        i && i()),
        this.lastTap = !1) : (this.lastTap = t,
        setTimeout(function() {
            this.lastTap = !1
        }
        .bind(this), 600))
    },
    beginDraw: function(t) {
        t.preventDefault(),
        t.changedTouches && this.detectDoubleTap(t.changedTouches[0], function() {
            this.zoomAndShift(this.state.first.x, this.state.first.y, 2),
            this.draw(this.img, this.state.shift.x(), this.state.shift.y()),
            this.refreshBuff()
        }),
        this.state.touchmove = !!(t.touches && t.touches.length > 1),
        this.firstDist = null,
        this.state.first = this.getCoord(t),
        this.state.touchmove ? (this.state.move = !1,
        this.box.sync(),
        this.refresh()) : (this.isPrint() ? this.printArea.pass(this.state.first.x, this.state.first.y) && (this.printArea.save(),
        this.printArea.drag = !0) : this.orbit.enabled && (0 === t.button || t.touches && 1 === t.touches.length) && this.orbit.init(this.ctx, this.state.first),
        this.state.move = !0)
    },
    endDraw: function(t) {
        this.firstDist = null,
        this.printArea && (this.printArea.drag = !1);
        var i = this.getCoord(t);
        if (this.state.first && Math.abs(i.x - this.state.first.x) < 8 && Math.abs(i.y - this.state.first.y) < 8) {
            var s = {};
            s.id = this.guid,
            this.state.move = !1,
            this.printArea && !this.printArea.pass(this.state.first.x, this.state.first.y) ? this.setClipParamsAndPrint(null) : this.measuring.mode ? "mouseout" != t.type && (this.state.snapPoint ? this.measuring.setCAD(this.state.snapPoint, t.button, !1) : this.measuring.set(this.state.first.x, this.state.first.y, t.button, !1)) : (s.x = this.state.first.x,
            s.y = this.state.first.y,
            s.isUnselect = 0 == this.box.extents.viewMode,
            this.state.infoEnabled ? cad.get(this, ["info", s], void 0, function(t) {
                this.doOnSelect(t),
                0 != this.box.extents.viewMode && this.refresh()
            }
            .bind(this), null, !1) : this.doOnSelectPoint(cad.math.screenToDC(i, this.drawMatrix)))
        }
        if (this.state.touchmove)
            this.state.touchmove = !!(t.touches && t.touches.length > 1),
            this.box.sync(),
            this.refresh();
        else if (this.orbit.moving)
            this.state.move = !1,
            this.orbit.finish(this.ctx);
        else if (this.state.move) {
            if (!this.isPrint()) {
                var e = this.getCoord(t).x - this.state.first.x
                  , n = this.getCoord(t).y - this.state.first.y;
                this.drawMatrix = cad.math.matTranslate(this.drawMatrix, e, n, 0)
            }
            this.state.move = !1,
            this.box.sync(),
            this.refresh()
        }
    },
    setClipParamsAndPrint: function(t) {
        var i = this.printArea.rect()
          , s = 1600 / i.w
          , e = {};
        if (e.id = this.guid,
        this.printArea.asPdf) {
            var n = this.state.clipRect(i);
            e.pgw = this.printArea.page.w,
            e.pgh = this.printArea.page.h,
            e.margin = this.printArea.page.margin,
            e.l = n.x,
            e.t = n.y,
            e.w = n.w,
            e.h = n.h,
            e.ext_opts = this.getOptString(this.printArea.options),
            this.printUrl = this.service + "/cliptopdf",
            this.printParams = e
        } else
            e.x = this.img.box.x - i.x,
            e.y = this.img.box.y - i.y,
            e.w = this.img.box.w,
            e.h = this.img.box.h,
            this.state.getParam(e),
            e.fc = "black",
            e.bc = "white",
            e.width = i.w,
            e.height = i.h,
            e.x *= s,
            e.y *= s,
            e.w *= s,
            e.h *= s,
            e.width *= s,
            e.height *= s,
            this.printUrl = this.service + "/image?" + this.makeParams(e),
            this.printParams = null,
            this.printArea = null;
        this.print(t)
    },
    showSnapRect: function(t) {
        var i = this.ctx.strokeStyle
          , s = this.ctx.fillStyle;
        this.redraw(),
        this.drawNext();
        try {
            "black" == this.getBackgroundColor() ? (this.ctx.strokeStyle = "white",
            this.ctx.fillStyle = "white") : (this.ctx.strokeStyle = "black",
            this.ctx.fillStyle = "black"),
            this.ctx.beginPath();
            try {
                switch (this.state.snapType) {
                case 0:
                    this.ctx.moveTo(t.x - 10, t.y - 10),
                    this.ctx.lineTo(t.x + 5, t.y - 10),
                    this.ctx.lineTo(t.x + 5, t.y - 5),
                    this.ctx.lineTo(t.x + 10, t.y - 5),
                    this.ctx.lineTo(t.x + 10, t.y + 10),
                    this.ctx.lineTo(t.x - 5, t.y + 10),
                    this.ctx.lineTo(t.x - 5, t.y + 5),
                    this.ctx.lineTo(t.x - 10, t.y + 5),
                    this.ctx.lineTo(t.x - 10, t.y - 10);
                    break;
                case 1:
                    this.ctx.moveTo(t.x - 10, t.y - 10),
                    this.ctx.lineTo(t.x + 10, t.y - 10),
                    this.ctx.lineTo(t.x + 10, t.y + 10),
                    this.ctx.lineTo(t.x - 10, t.y + 10),
                    this.ctx.lineTo(t.x - 10, t.y - 10);
                    break;
                case 2:
                    this.ctx.moveTo(t.x + 10, t.y),
                    this.ctx.arc(t.x, t.y, 10, 0, 2 * Math.PI);
                    break;
                case 4:
                    this.ctx.moveTo(t.x, t.y - 10),
                    this.ctx.lineTo(t.x + 10, t.y + 10),
                    this.ctx.lineTo(t.x - 10, t.y + 10),
                    this.ctx.lineTo(t.x, t.y - 10);
                    break;
                case 5:
                    this.ctx.moveTo(t.x - 10, t.y - 10),
                    this.ctx.lineTo(t.x + 10, t.y - 10),
                    this.ctx.lineTo(t.x + 10, t.y + 10);
                    break;
                case 6:
                    this.ctx.moveTo(t.x - 10, t.y - 10),
                    this.ctx.lineTo(t.x, t.y),
                    this.ctx.lineTo(t.x + 10, t.y - 10),
                    this.ctx.lineTo(t.x - 10, t.y - 10),
                    this.ctx.moveTo(t.x - 10, t.y + 10),
                    this.ctx.lineTo(t.x, t.y),
                    this.ctx.lineTo(t.x + 10, t.y + 10),
                    this.ctx.lineTo(t.x - 10, t.y + 10);
                    break;
                case 7:
                    this.ctx.moveTo(t.x - 10, t.y - 10),
                    this.ctx.lineTo(t.x + 10, t.y + 10),
                    this.ctx.moveTo(t.x + 10, t.y - 10),
                    this.ctx.lineTo(t.x - 10, t.y + 10);
                    break;
                case 17:
                    this.ctx.moveTo(t.x, t.y - 10),
                    this.ctx.lineTo(t.x + 10, t.y),
                    this.ctx.lineTo(t.x, t.y + 10),
                    this.ctx.lineTo(t.x - 10, t.y),
                    this.ctx.lineTo(t.x, t.y - 10)
                }
            } finally {
                this.ctx.stroke()
            }
        } finally {
            this.ctx.strokeStyle = i,
            this.ctx.fillStyle = s
        }
    },
    moveEvent: function(t) {
        t.preventDefault();
        var i = this.getCoord(t);
        if (t.touches && 2 == t.touches.length) {
            var s = {
                x: (t.touches[0].pageX + t.touches[1].pageX) / 2,
                y: (t.touches[0].pageY + t.touches[1].pageY) / 2
            }
              , e = cad.math.calcDist(t.touches[0], t.touches[1]);
            if (this.firstDist) {
                var n = e / this.firstDist;
                this.box.pic.w = this.img.width * n,
                this.box.pic.h = this.img.height * n,
                this.box.pic.x = s.x - this.firstMid.x,
                this.box.pic.y = s.y - this.firstMid.y;
                var a = s.x / this.img.width
                  , h = s.y / this.img.height;
                this.box.pic.x += -(this.box.pic.w - this.img.width) * a,
                this.box.pic.y += -(this.box.pic.h - this.img.height) * h,
                this.draw(this.img),
                this.box.w = this.box.saved.w,
                this.box.h = this.box.saved.h,
                this.box.z = this.box.saved.z,
                this.drawMatrix = cad.utils.copyArray(this.firstMatrix),
                this.box.zoom(n, s)
            } else
                this.firstDist = e,
                this.firstMid = s,
                this.firstMatrix = cad.utils.copyArray(this.drawMatrix),
                this.box.save()
        } else if (this.state.move) {
            if (this.isPrint() && this.printArea.drag) {
                this.printArea.rect();
                var r = this.printArea.savedPos();
                this.printArea.moveTo(r.x + (i.x - this.state.x()), r.y + (i.y - this.state.y()))
            } else
                this.orbit.moving && (0 === t.button || t.touches && 1 === t.touches.length) ? this.orbit.move(this.ctx, i) : (this.box.pic.x = i.x - this.state.x(),
                this.box.pic.y = i.y - this.state.y());
            this.draw(this.img)
        } else
            this.redraw(),
            this.measuring.draw(this.ctx, i.x, i.y);
        if (this.isShowSnap) {
            var o = {};
            this.state.move ? this.state.snapPoint && this.showSnapRect(this.state.fromCAD(this.state.snapPoint)) : (o.id = this.guid,
            o.x = i.x,
            o.y = i.y,
            o.w = this.canvas.width,
            o.h = this.canvas.height,
            cad.get(this, ["snapdata", o], void 0, function(t) {
                if (this.state.snapPoint = !1,
                this.state.snapType = !1,
                t && t.isShow) {
                    this.box.extents;
                    this.state.snapPoint = {
                        x: t.CADPoint.X,
                        y: t.CADPoint.Y,
                        z: 0
                    },
                    this.state.snapType = t.SnapType;
                    var i = this.state.fromCAD(this.state.snapPoint);
                    this.showSnapRect(i)
                } else
                    this.canvas.style.cursor = this.cursorStyle
            }
            .bind(this), null, !1))
        }
    },
    zoomAndShift: function(t, i, s) {
        this.state.shift.coef *= s;
        this.state.shift.zoom.x = this.box.x * (1 - this.state.shift.coef),
        this.state.shift.zoom.y = this.box.y * (1 - this.state.shift.coef),
        this.state.shift.mouse.x = (t - this.box.x) / this.box.w * (this.box.w * (1 - this.state.shift.coef)),
        this.state.shift.mouse.y = (i - this.box.y) / this.box.h * (this.box.h * (1 - this.state.shift.coef)),
        this.box.zoom(s, {
            x: t,
            y: i
        }),
        function(t, i) {
            t.h ? t.h *= i : t.height *= i,
            t.w ? t.w *= i : t.width *= i
        }(this.img, s)
    },
    mouseWhell: function(t) {
        if (!(this.settings && this.settings.noscroll || this.state.move)) {
            t.preventDefault();
            var i = (t = t || window.event).detail ? t.detail : t.wheelDelta;
            (cad.utils.browser.opera || cad.utils.browser.firefox) && (i *= -1);
            var s = t.offsetX ? t.offsetX : t.layerX
              , e = t.offsetY ? t.offsetY : t.layerY
              , n = t.shiftKey ? 2 : 1.25;
            i < 0 && (n = 1 / n),
            cad.utils.browser.firefox && (s -= t.currentTarget.offsetLeft,
            e -= t.currentTarget.offsetTop),
            this.img && (this.zoomAndShift(s, e, n),
            this.draw(this.img, this.state.shift.x(), this.state.shift.y()),
            this.refreshBuff())
        }
    },
    setInfoEnabled: function() {
        if (this.state.setInfoEnabled(),
        !this.state.infoEnabled) {
            this.doOnSelect(null);
            var t = {
                id: this.guid
            };
            0 != this.box.extents.viewMode && cad.get(this, ["unselect", t], void 0, function(t) {
                this.refresh()
            }
            .bind(this), null, !1)
        }
    },
    doOnSelect: function(t) {
        "function" == typeof this.onselect && this.onselect(t)
    },
    doOnSelectPoint: function(t) {
        "function" == typeof this.onselectpoint && this.onselectpoint(t)
    },
    alert: function(t) {
        "function" == typeof this.onerror && this.onerror(t)
    },
    clear: function() {
        this.ctx.save();
        try {
            this.ctx.fillStyle = this.getBackgroundColor(),
            this.ctx.globalAlpha = 1,
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height)
        } finally {
            this.ctx.restore()
        }
    },
    getOptString: function(t) {
        var i = null;
        return i = this.serverOptions.bUseExtendedPrintDialog ? {
            ScaleName: this.settings.ScaleName,
            CADsInUnit: this.settings.CADsInUnit,
            printInfo: t,
            measures: this.measuring.lastSegments
        } : {
            ScaleName: "",
            CADsInUnit: 1,
            measures: this.measuring.lastSegments
        },
        JSON.stringify(i)
    },
    redraw: function() {
        this.clear(),
        this.ctx && this.lastDraw && (this.ctx.drawImage.apply(this.ctx, this.lastDraw),
        this.drawNext.apply(this, this.lastDraw))
    },
    draw: function(t, i, s, e, n, a) {
        this.drawing = !0;
        try {
            this.clear(),
            this.orbit.moving || (t ? a ? this.drawImage(t, i, s, e * a, n * a) : n ? this.drawImage(t, i, s, e, n) : s ? this.drawImage(t, i, s, t.width, t.height) : this.drawImage(t, this.box.pic.x, this.box.pic.y, this.box.pic.w, this.box.pic.h) : this.img && this.drawImage(this.img, this.box.pic.x, this.box.pic.y, this.box.pic.w, this.box.pic.h)),
            this.drawNext.apply(this, arguments)
        } finally {
            this.drawing = !1
        }
    },
    drawNext: function(t, i, s, e, n, a) {
        if (this.isPrint() && !i) {
            var h = function(t, i) {
                this.ctx.beginPath(),
                this.drawPolyline(t, function(t) {
                    return cad.math.transformPoint(t, i)
                }
                .bind(this)),
                this.ctx.fill()
            }
            .bind(this)
              , r = this.ctx.globalAlpha;
            this.ctx.globalAlpha = .5,
            this.ctx.fillStyle = "#b0c2f7";
            var o = this.printArea.rect()
              , c = Math.min(o.w, o.h) / 32;
            this.ctx.fillRect(o.x, o.y, o.w, o.h);
            var l = {
                x: o.x + o.w / 2,
                y: o.y + o.h / 2,
                z: 0
            }
              , d = cad.math.matByTranslate(l.x, l.y, l.z);
            if (d = cad.math.matXMat(cad.math.matByScale(c), d),
            this.ctx.fillStyle = "#FFF500",
            this.ctx.globalAlpha = .7,
            h([{
                x: -1,
                y: 1,
                z: 0
            }, {
                x: -1,
                y: 5,
                z: 0
            }, {
                x: -2,
                y: 5,
                z: 0
            }, {
                x: 0,
                y: 8,
                z: 0
            }, {
                x: 2,
                y: 5,
                z: 0
            }, {
                x: 1,
                y: 5,
                z: 0
            }, {
                x: 1,
                y: 1,
                z: 0
            }, {
                x: 5,
                y: 1,
                z: 0
            }, {
                x: 5,
                y: 2,
                z: 0
            }, {
                x: 8,
                y: 0,
                z: 0
            }, {
                x: 5,
                y: -2,
                z: 0
            }, {
                x: 5,
                y: -1,
                z: 0
            }, {
                x: 1,
                y: -1,
                z: 0
            }, {
                x: 1,
                y: -5,
                z: 0
            }, {
                x: 2,
                y: -5,
                z: 0
            }, {
                x: 0,
                y: -8,
                z: 0
            }, {
                x: -2,
                y: -5,
                z: 0
            }, {
                x: -1,
                y: -5,
                z: 0
            }, {
                x: -1,
                y: -1,
                z: 0
            }, {
                x: -5,
                y: -1,
                z: 0
            }, {
                x: -5,
                y: -2,
                z: 0
            }, {
                x: -8,
                y: 0,
                z: 0
            }, {
                x: -5,
                y: 2,
                z: 0
            }, {
                x: -5,
                y: 1,
                z: 0
            }, {
                x: -1,
                y: 1,
                z: 0
            }], d),
            this.printIcon)
                try {
                    l = {
                        x: o.x + 1.05 * o.w,
                        y: o.y + .2 * o.h,
                        z: 0
                    };
                    d = cad.math.matByTranslate(l.x, l.y, l.z),
                    d = cad.math.matXMat(cad.math.matByScale(c), d),
                    this.ctx.fillStyle = "#D2691E",
                    h([{
                        x: 0,
                        y: 1.3,
                        z: 0
                    }, {
                        x: 7,
                        y: 1.3,
                        z: 0
                    }, {
                        x: 7,
                        y: 2.3,
                        z: 0
                    }, {
                        x: 10,
                        y: 0,
                        z: 0
                    }, {
                        x: 7,
                        y: -2.3,
                        z: 0
                    }, {
                        x: 7,
                        y: -1.3,
                        z: 0
                    }, {
                        x: 0,
                        y: -1.3,
                        z: 0
                    }, {
                        x: 0,
                        y: 1.3,
                        z: 0
                    }], d);
                    var u = .25 * o.w
                      , x = cad.math.transformPoint({
                        x: 12,
                        y: 0,
                        z: 0
                    }, d);
                    x.y = x.y - u / 2,
                    this.ctx.drawImage(this.printIcon, x.x, x.y, u, u)
                } catch (t) {
                    console.log("outer print icon draw error", t)
                }
            this.ctx.globalAlpha = r
        }
        this.measuring.draw(this.ctx),
        this.orbit.draw(this.ctx)
    },
    drawImage: function(t, i, s, e, n) {
        this.lastDraw = [t, i, s, e, n],
        this.ctx.drawImage(t, i, s, e, n)
    },
    calcAngle: function(t, i) {
        return cad.geom.calcAngle(t, i)
    },
    drawPolyline: function(t, i, s) {
        s || (s = this.ctx),
        cad.draw.drawPolyline(t, i, s)
    },
    drawText: function(t, i, s) {
        this.ctx.save();
        try {
            this.ctx.fillStyle = "rgb(0,0,255)",
            this.ctx.font = "11px Arial",
            this.ctx.fillText(s, t, i)
        } finally {
            this.ctx.restore()
        }
    },
    imageUrl: function(t, i) {
        var s = {};
        if (t || (t = this.box),
        s.id = this.guid,
        s.fc = cad.utils.colorToHex(this.getForegroundColor()),
        s.bc = cad.utils.colorToHex(this.getBackgroundColor()),
        this.state.getParam(s),
        s.width = this.canvas.width,
        s.height = this.canvas.height,
        s.matrix = JSON.stringify(this.drawMatrix),
        this.buff.box = {
            x: t.x + this.state.shift.mouse.x,
            y: t.y + this.state.shift.mouse.y,
            w: t.w,
            h: t.h,
            z: this.box.z,
            extents: this.box.extents
        },
        !i)
            return this.service + "/image?" + this.makeParams(s);
        s.file = i,
        cad.get(this, ["saveto", s], void 0, null, null, !1)
    },
    setViewScreenRect: function(t, i, s) {
        var e = cad.math.screenToDC({
            x: t,
            y: i
        }, this.drawMatrix);
        this.setCurrentViewState(e.x, e.y, e.z, s)
    },
    setCurrentViewState: function(t, i, s, e) {
        var n = {};
        n.id = this.guid,
        n.x = t,
        n.y = i,
        n.z = s;
        var a = this.img.box.extents
          , h = a.h * this.canvas.width / (a.w * this.canvas.height);
        h > 1 ? (n.w = this.canvas.width / h,
        n.h = this.canvas.height) : (n.w = this.canvas.width,
        n.h = this.canvas.height * h),
        n.w = n.w * e / 100,
        n.h = n.h * e / 100,
        n.cx = this.canvas.width / 2,
        n.cy = this.canvas.height / 2,
        cad.get(this, ["getnewmatrix", n], void 0, function(t) {
            t && (this.drawMatrix = t.matrix)
        }
        .bind(this), null, !1),
        this.buff.src = this.imageUrl()
    },
    getCurrentViewState: function() {
        var t = this.canvas.width / 2
          , i = this.canvas.height / 2
          , s = cad.math.screenToDC({
            x: t,
            y: i
        }, this.drawMatrix)
          , e = {};
        e.x = s.x,
        e.y = s.y,
        e.z = s.z;
        var n = this.canvas.width / this.box.extents.w
          , a = this.canvas.height / this.box.extents.h;
        return e.zoom = a < n ? this.drawMatrix[1][1] / a : this.drawMatrix[0][0] / n,
        e.zoom = 100 * Math.abs(e.zoom),
        e
    },
    pdfUrl: function(t, i, s) {
        var e = !0
          , n = this.service + "/printtopdf";
        return s || (e = !1,
        s = {}),
        t || (t = this.PrintArea.page),
        s.id = this.guid,
        s.pgw = t.w,
        s.pgh = t.h,
        s.margin = t.margin,
        s.ext_opts = this.getOptString(i),
        e ? n : n + "?" + this.makeParams(s)
    },
    printRect: function(t, i, s, e) {
        this.printArea = new cad.PrintArea(this,t,i,s,null,e),
        this.draw()
    },
    printCurentView: function(t, i, s, e) {
        this.printArea = new cad.PrintArea(this,{
            x: 0,
            y: 0,
            w: this.img.width,
            h: this.img.height
        },i,s,null,e,!0),
        this.setClipParamsAndPrint(t)
    },
    print: function(t, i, s, e) {
        try {
            !s && this.printArea && this.printArea.asPdf && (s = this.printArea.asPdf),
            !i && this.printArea && this.printArea.page && (i = this.printArea.page);
            var n = {};
            if (s ? this.printUrl || (this.printUrl = this.pdfUrl(i, e, n),
            this.printParams = n) : (this.printUrl = this.printUrl ? this.printUrl : this.imageUrl(),
            this.printParams = n),
            t || (t = this.onprint),
            "function" == typeof t && !t(this))
                return;
            if (s) {
                if (this.settings.isUsePostMethodForPdfPrint) {
                    var a = document.createElement("form");
                    for (var h in a.target = "_blank",
                    a.method = "POST",
                    a.action = this.printUrl,
                    a.style.display = "none",
                    this.printParams) {
                        var r = document.createElement("input");
                        r.type = "hidden",
                        r.name = h,
                        r.value = this.printParams[h],
                        a.appendChild(r)
                    }
                    document.body.appendChild(a),
                    a.submit(),
                    document.body.removeChild(a)
                } else
                    var o = window.open(this.printUrl + "?" + this.makeParams(this.printParams));
                this.printIsPdf = !1
            } else {
                (o = window.open("", "_blank")).document.write('<img src="' + this.printUrl + '" onload="window.print(); window.close()" onerror="window.close()"/>'),
                o.document.close()
            }
        } finally {
            this.printUrl = null,
            this.printParams = null,
            this.printArea = null
        }
    },
    refresh: function(t, i, s) {
        this.guid && this.guid.length < 9 || !this.guid || (i && this.box.init(),
        s && (this.box.x = (this.canvas.width - this.box.w) / 2,
        this.box.y = (this.canvas.height - this.box.h) / 2),
        this.buff = new Image,
        this.buff.onload = function(i) {
            if (this.buff && this.buff == i.target)
                try {
                    this.img = this.buff,
                    this.img.timeStamp = i.timeStamp,
                    this.box.pic.reset()
                } finally {
                    this.buff.box ? (this.box.assign(this.buff.box),
                    this.state.shift.reset()) : this.box.syncm(),
                    "function" == typeof t ? t() : this.draw(this.img, 0, 0)
                }
        }
        .bind(this),
        this.buff.onerror = function(t) {
            cad.get(this, ["isalive", {
                id: this.guid
            }], void 0, function(t) {
                t && t.ok && !t.alive && this.alert && this.alert("unloaded")
            }
            .bind(this), function(t) {
                this.alert && this.alert("Service is down")
            }, !1)
        }
        .bind(this),
        this.buff.src = this.imageUrl())
    },
    refreshBuff: function() {
        this.timer && clearTimeout(this.timer),
        this.timer = setTimeout(this.refresh.bind(this), 100)
    },
    resize: function(t) {
        void 0 !== t && t.target != window || (this.canvas.init(t),
        this.refreshBuff())
    },
    unload: function() {
        cad.get(this, ["unload", {
            id: this.guid
        }])
    },
    show: function(t, i) {
        t && (this.guid = t,
        this.settings.init(),
        this.refresh(null, !0, i),
        this.face && (this.face.guid = t))
    }
};
